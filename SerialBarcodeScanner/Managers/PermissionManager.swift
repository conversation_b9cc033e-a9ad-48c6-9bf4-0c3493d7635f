import Foundation
import AVFoundation
import Photos

// MARK: - Permission Manager
// This class will be implemented in Step 2: Camera Integration and Permissions

class PermissionManager: ObservableObject {
    @Published var cameraPermissionStatus: AVAuthorizationStatus = .notDetermined
    @Published var photoLibraryPermissionStatus: PHAuthorizationStatus = .notDetermined
    
    init() {
        updatePermissionStatuses()
    }
    
    // MARK: - Camera Permissions
    func requestCameraPermission() async -> Bool {
        // Implementation will be added in Step 2
        return false
    }
    
    func checkCameraPermission() -> AVAuthorizationStatus {
        // Implementation will be added in Step 2
        return .notDetermined
    }
    
    // MARK: - Photo Library Permissions
    func requestPhotoLibraryPermission() async -> Bool {
        // Implementation will be added in Step 2
        return false
    }
    
    func checkPhotoLibraryPermission() -> PHAuthorizationStatus {
        // Implementation will be added in Step 2
        return .notDetermined
    }
    
    // MARK: - Private Methods
    private func updatePermissionStatuses() {
        cameraPermissionStatus = AVCaptureDevice.authorizationStatus(for: .video)
        photoLibraryPermissionStatus = PHPhotoLibrary.authorizationStatus(for: .readWrite)
    }
}

// MARK: - Permission Status Extensions
extension AVAuthorizationStatus {
    var isAuthorized: Bool {
        return self == .authorized
    }
    
    var isDenied: Bool {
        return self == .denied || self == .restricted
    }
}

extension PHAuthorizationStatus {
    var isAuthorized: Bool {
        return self == .authorized || self == .limited
    }
    
    var isDenied: Bool {
        return self == .denied || self == .restricted
    }
}

import Foundation
import Vision

// MARK: - Data Models for Scan Results
// These models will be implemented in Step 3: Vision Framework Integration

struct ScanResult {
    let serialText: TextDetectionResult?
    let associatedBarcode: BarcodeDetectionResult?
    let confidence: Double
    let processingTime: TimeInterval
    let allDetectedText: [TextDetectionResult]
    let allDetectedBarcodes: [BarcodeDetectionResult]
}

struct TextDetectionResult {
    let text: String
    let boundingBox: CGRect
    let confidence: Float
    let observation: VNRecognizedTextObservation
}

struct BarcodeDetectionResult {
    let value: String
    let symbology: VNBarcodeSymbology
    let boundingBox: CGRect
    let confidence: Float
    let observation: VNBarcodeObservation
}

struct AssociationResult {
    let textResult: TextDetectionResult
    let barcodeResult: BarcodeDetectionResult
    let distance: Double
    let alignmentScore: Double
    let directionScore: Double
    let overallConfidence: Double
}

// MARK: - Extensions for convenience
extension ScanResult {
    var isValid: Bool {
        return serialText != nil && associatedBarcode != nil && confidence > 0.5
    }
    
    var decodedValue: String? {
        return associatedBarcode?.value
    }
}

extension TextDetectionResult {
    var isSerialNumber: Bool {
        let lowercased = text.lowercased()
        return lowercased.contains("serial") || 
               lowercased.contains("s/n") ||
               lowercased.contains("ser no")
    }
}

// swift-tools-version: 5.7
import PackageDescription

let package = Package(
    name: "SerialBarcodeScanner",
    platforms: [
        .iOS(.v15)
    ],
    products: [
        .library(
            name: "SerialBarcodeScanner",
            targets: ["SerialBarcodeScanner"]),
    ],
    dependencies: [
        // No external dependencies - using only Apple frameworks
    ],
    targets: [
        .target(
            name: "SerialBarcodeScanner",
            dependencies: [],
            path: ".",
            sources: [
                "SerialBarcodeScannerApp.swift",
                "ContentView.swift",
                "Models/ScanResult.swift",
                "Managers/PermissionManager.swift"
            ]
        ),
        .testTarget(
            name: "SerialBarcodeScannerTests",
            dependencies: ["SerialBarcodeScanner"]),
    ]
)

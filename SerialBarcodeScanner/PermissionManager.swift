import Foundation
import AVFoundation
import Photos

// MARK: - Permission Manager
class PermissionManager: ObservableObject {
    @Published var cameraPermissionStatus: AVAuthorizationStatus = .notDetermined
    @Published var photoLibraryPermissionStatus: PHAuthorizationStatus = .notDetermined
    
    init() {
        updatePermissionStatuses()
    }
    
    // MARK: - Camera Permissions
    func requestCameraPermission() async -> Bool {
        let status = AVCaptureDevice.authorizationStatus(for: .video)
        
        switch status {
        case .authorized:
            await MainActor.run {
                self.cameraPermissionStatus = .authorized
            }
            return true
            
        case .notDetermined:
            let granted = await AVCaptureDevice.requestAccess(for: .video)
            await MainActor.run {
                self.cameraPermissionStatus = AVCaptureDevice.authorizationStatus(for: .video)
            }
            return granted
            
        case .denied, .restricted:
            await MainActor.run {
                self.cameraPermissionStatus = status
            }
            return false
            
        @unknown default:
            await MainActor.run {
                self.cameraPermissionStatus = status
            }
            return false
        }
    }
    
    func checkCameraPermission() -> AVAuthorizationStatus {
        let status = AVCaptureDevice.authorizationStatus(for: .video)
        cameraPermissionStatus = status
        return status
    }
    
    // MARK: - Photo Library Permissions
    func requestPhotoLibraryPermission() async -> Bool {
        let status = PHPhotoLibrary.authorizationStatus(for: .readWrite)
        
        switch status {
        case .authorized, .limited:
            await MainActor.run {
                self.photoLibraryPermissionStatus = status
            }
            return true
            
        case .notDetermined:
            let newStatus = await PHPhotoLibrary.requestAuthorization(for: .readWrite)
            await MainActor.run {
                self.photoLibraryPermissionStatus = newStatus
            }
            return newStatus == .authorized || newStatus == .limited
            
        case .denied, .restricted:
            await MainActor.run {
                self.photoLibraryPermissionStatus = status
            }
            return false
            
        @unknown default:
            await MainActor.run {
                self.photoLibraryPermissionStatus = status
            }
            return false
        }
    }
    
    func checkPhotoLibraryPermission() -> PHAuthorizationStatus {
        let status = PHPhotoLibrary.authorizationStatus(for: .readWrite)
        photoLibraryPermissionStatus = status
        return status
    }
    
    // MARK: - Private Methods
    private func updatePermissionStatuses() {
        cameraPermissionStatus = AVCaptureDevice.authorizationStatus(for: .video)
        photoLibraryPermissionStatus = PHPhotoLibrary.authorizationStatus(for: .readWrite)
    }
}

// MARK: - Permission Status Extensions
extension AVAuthorizationStatus {
    var isAuthorized: Bool {
        return self == .authorized
    }
    
    var isDenied: Bool {
        return self == .denied || self == .restricted
    }
    
    var displayText: String {
        switch self {
        case .authorized:
            return "Authorized"
        case .denied:
            return "Denied"
        case .restricted:
            return "Restricted"
        case .notDetermined:
            return "Not Determined"
        @unknown default:
            return "Unknown"
        }
    }
}

extension PHAuthorizationStatus {
    var isAuthorized: Bool {
        return self == .authorized || self == .limited
    }
    
    var isDenied: Bool {
        return self == .denied || self == .restricted
    }
    
    var displayText: String {
        switch self {
        case .authorized:
            return "Authorized"
        case .limited:
            return "Limited Access"
        case .denied:
            return "Denied"
        case .restricted:
            return "Restricted"
        case .notDetermined:
            return "Not Determined"
        @unknown default:
            return "Unknown"
        }
    }
}

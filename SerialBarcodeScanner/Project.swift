// Project Configuration for Serial Barcode Scanner POC
// This file serves as documentation for the project structure and requirements

/*
 Project: Serial Barcode Scanner POC
 Target: iOS 15.0+
 Language: Swift
 UI Framework: SwiftUI
 
 Required Frameworks:
 - Vision (for text recognition and barcode detection)
 - AVFoundation (for camera functionality)
 - Photos (for photo library access)
 - SwiftUI (for user interface)
 
 Bundle Identifier: com.example.serialbarcodescanner
 Display Name: Serial Barcode Scanner
 
 Key Features:
 1. Camera-based barcode scanning
 2. Photo library image processing
 3. Text recognition for "Serial No." detection
 4. Spatial association of text and barcodes
 5. Visual feedback with overlays
 6. On-device processing only
 
 Architecture:
 - MVVM pattern with SwiftUI
 - Separate managers for permissions, camera, and vision processing
 - Modular design for easy testing and maintenance
 
 Performance Requirements:
 - Processing time: ≤3 seconds for clear images
 - Accuracy: ≥80% for text and barcode association
 - Memory efficient for real-time camera processing
 
 File Structure:
 - SerialBarcodeScannerApp.swift (main app entry point)
 - ContentView.swift (main UI)
 - PermissionManager.swift (camera/photo permissions)
 - CameraViewController.swift (camera handling)
 - VisionProcessor.swift (ML processing)
 - BarcodeAssociationEngine.swift (spatial logic)
 - ScanResult.swift (data models)
 - Supporting views and utilities
 */

# Serial Barcode Scanner POC

A Proof of Concept iOS application that identifies and decodes barcodes associated with "Serial No." text on labels using on-device machine learning.

## Overview

This POC demonstrates the feasibility of accurately identifying and decoding a specific barcode based on its proximity to predefined text ("Serial No.") using Apple's Vision framework on iOS devices.

## Features

- **Camera-based scanning**: Real-time barcode detection using device camera
- **Photo library support**: Process images from photo library for testing
- **Text recognition**: Detect "Serial No." text and variations
- **Barcode detection**: Support for Code 128, QR Code, EAN, UPC formats
- **Spatial association**: Intelligent matching of text and nearby barcodes
- **Visual feedback**: Highlight detected elements with overlays
- **On-device processing**: All ML processing happens locally

## Requirements

- iOS 15.0+
- Xcode 13.0+
- Swift 5.5+
- Physical device with camera (for camera functionality)

## Architecture

### Core Components

1. **SerialBarcodeScannerApp.swift** - Main app entry point
2. **ContentView.swift** - Primary user interface
3. **PermissionManager.swift** - Handle camera and photo permissions
4. **CameraViewController.swift** - Camera capture and preview
5. **VisionProcessor.swift** - Vision framework integration
6. **BarcodeAssociationEngine.swift** - Spatial analysis logic
7. **ScanResult.swift** - Data models

### Frameworks Used

- **Vision**: Text recognition and barcode detection
- **AVFoundation**: Camera functionality
- **Photos**: Photo library access
- **SwiftUI**: User interface

## Implementation Status

- [x] Step 1: Project Setup and Basic Structure
- [ ] Step 2: Camera Integration and Permissions
- [ ] Step 3: Vision Framework Integration
- [ ] Step 4: Spatial Association Logic
- [ ] Step 5: Photo Library Integration
- [ ] Step 6: Visual Feedback and UI Polish
- [ ] Step 7: Testing and Validation

## Success Criteria

- ≥80% accuracy in identifying "Serial No." text
- ≥80% accuracy in barcode association
- Processing time ≤3 seconds for clear images
- Stable performance on iOS devices

## Usage

1. Launch the app
2. Grant camera and photo library permissions
3. Choose "Scan with Camera" for real-time scanning
4. Or choose "Select from Photos" to process existing images
5. Point camera at label or select image with "Serial No." text and barcode
6. View decoded barcode value in results

## Development Notes

- Focus on POC functionality over polished UI
- All processing is on-device for privacy and performance
- Modular design for easy testing and maintenance
- Comprehensive error handling for edge cases

## Testing

The app includes test cases for:
- Various label layouts and designs
- Different lighting conditions
- Multiple barcode scenarios
- Text variations and edge cases

## Future Considerations

- Support for additional text patterns
- Enhanced accuracy for challenging conditions
- Performance optimization for older devices
- Production-ready UI/UX improvements
